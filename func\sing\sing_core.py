# 唱歌功能
from func.tools.singleton_mode import singleton
from func.log.default_log import DefaultLog
from func.gobal.data import SingData
from func.obs.obs_init import ObsInit
from func.tools.string_util import StringUtil

from func.tts.tts_core import TTsCore
from func.score.oper_score import OperScore
from func.image.image_core import ImageCore

from func.obs.obs_websocket import ObsWebSocket,VideoStatus,VideoControl
from func.vtuber.emote_oper import EmoteOper
from func.vtuber.action_oper import ActionOper
from func.tts.player import MpvPlay

# 导入本地音乐转换服务
from func.sing.music_convert_service import music_convert_service

import requests
import json
import time
import os
import re
from threading import Thread

@singleton
class SingCore:
    # 设置控制台日志
    log = DefaultLog().getLogger()

    singData = SingData()  # 唱歌数据
    # llmData = LLmData()  # llm数据

    ttsCore = TTsCore()  # 语音核心
    imageCore = ImageCore()  # 图片核心
    emoteOper = EmoteOper()  # 表情
    actionOper = ActionOper()  # 动作
    mpvPlay = MpvPlay()  # 播放器

    operScore = OperScore()  # 积分

    def __init__(self):
        self.obs = ObsInit().get_ws()

    # 唱歌
    def singTry(self,songname, username):
        try:
            if songname != "":
                self.sing(songname, username)
        except Exception as e:
            print(e)
            self.log.exception(f"【singTry】发生了异常：")
            self.singData.is_singing = 2
            self.singData.is_creating_song = 2

    # 唱歌
    def sing(self, songname, username):
        is_created = 0  # 1.已经生成过 0.没有生成过 2.生成失败

        query = songname  # 查询内容

        # =============== 开始-获取真实歌曲名称 =================
        # musicJson = requests.get(url=f"{self.singData.singUrl}/musicInfo/{query}", timeout=(5, 10))
        # music_json = json.loads(musicJson.text)
        # id = music_json["id"]
        # songname = music_json["songName"]
        # # 前置信息说明
        font_text = ""
        if query.lower().replace(" ", "") != songname.lower().replace(" ", ""):
            font_text = f'根据"{query}"的信息，'

        # if id == 0:
        #     outputTxt = f"{font_text}歌库不存在《{query}》这首歌曲哦"
        #     self.log.info(outputTxt)
        #     self.ttsCore.tts_say(outputTxt)
        #     return
        song_path = f"./output/{songname}/"
        # =============== 结束-获取真实歌曲名称 =================

        # =============== 开始-重复点播判断 =================
        if self.exist_song_queues(self.singData.SongMenuList, songname) == True:
            outputTxt = (
                f"回复{username}：{font_text}歌单里已经有歌曲《{songname}》，请勿重新点播"
            )
            self.ttsCore.tts_say(outputTxt)
            return
        # =============== 结束-重复点播判断 =================

        # =============== 开始-判断本地是否有歌 =================
        if os.path.exists(f"{song_path}/accompany.wav") or os.path.exists(
                f"{song_path}/vocal.wav"
        ):
            self.log.info(f"找到存在本地歌曲:{song_path}")
            outputTxt = f"回复{username}：{font_text}会唱《{songname}》这首歌曲哦"#{self.llmData.Ai_Name}
            self.ttsCore.tts_say(outputTxt)
            is_created = 1
        # =============== 结束-判断本地是否有歌 =================

        # =============== 开始：如果不存在歌曲，生成歌曲 =================
        if is_created == 0:
            # 播报学习歌曲
            self.log.info(f"歌曲不存在，需要生成歌曲《{songname}》")
            outputTxt = f"回复{username}：{font_text}需要学唱歌曲《{songname}》，请耐心等待"#{self.llmData.Ai_Name}
            self.ttsCore.tts_say(outputTxt)

            # 其他歌曲在生成的时候等待
            while self.singData.is_creating_song == 1:
                time.sleep(1)
            # 调用Ai学唱歌服务：生成歌曲
            is_created = self.create_song(songname, query, song_path, is_created)
        if is_created == 2:
            self.log.info(f"生成歌曲失败《{songname}》")
            return
        self.obs.show_text("状态提示", f"已经学会歌曲《{songname}》")#{self.llmData.Ai_Name}
        # =============== 结束：如果不存在歌曲，生成歌曲 =================

        # 等待播放
        self.log.info(f"等待播放{username}点播的歌曲《{songname}》：{self.singData.is_singing}")
        # 加入播放歌单
        self.singData.SongMenuList.put({"username": username, "songname": songname, "is_created": is_created, "song_path": song_path,
                          "query": query})

    # 播放歌曲清单
    def check_playSongMenuList(self):
        if not self.singData.SongMenuList.empty() and self.singData.is_singing == 2:
            # 播放歌曲
            self.singData.play_song_lock.acquire()
            mlist = self.singData.SongMenuList.get()  # 取出歌单播放
            self.singData.SongNowName = mlist  # 赋值当前歌曲名称
            self.singData.is_singing = 1  # 开始唱歌
            # =============== 开始：播放歌曲 =================
            self.obs.control_video("背景音乐", VideoControl.PAUSE.value)
            self.play_song(mlist["is_created"], mlist["songname"], mlist["song_path"], mlist["username"], mlist["query"])
            if self.singData.SongMenuList.qsize() == 0:
                self.obs.control_video("背景音乐", VideoControl.PLAY.value)
            # =============== 结束：播放歌曲 =================
            self.singData.is_singing = 2  # 完成唱歌
            self.singData.SongNowName = {}  # 当前播放歌单清空
            self.singData.play_song_lock.release()

    # 开始生成歌曲
    def create_song(self, songname, query, song_path, is_created):
        try:
            # =============== 开始生成歌曲 =================
            self.singData.create_song_lock.acquire()
            self.singData.is_creating_song = 1
            status_json = {}
            is_download = False

            # =============== 开始-选择二、学习唱歌任务 =================
            if is_download == False:
                # 直接调用本地音乐转换服务
                try:
                    if music_convert_service.music_module:
                        status, songname_result = music_convert_service.music_module.add_conversion_task(
                            music_info=query,
                            speaker=music_convert_service.speaker
                        )
                        status_json = {"status": status, "songName": songname_result}
                        self.log.info(f"本地音乐转换服务调用成功: {status_json}")
                    else:
                        self.log.error("音乐转换模块未初始化")
                        status_json = {"status": "error", "songName": query}
                except Exception as e:
                    self.log.error(f"调用本地音乐转换服务失败: {e}")
                    # 如果本地服务失败，回退到HTTP请求
                    try:
                        jsonStr = requests.get(url=f"{self.singData.singUrl}/append_song/{query}", timeout=(5, 10))
                        status_json = json.loads(jsonStr.text)
                        self.log.info(f"回退到HTTP请求成功: {status_json}")
                    except Exception as http_e:
                        self.log.error(f"HTTP请求也失败: {http_e}")
                        status_json = {"status": "error", "songName": query}
            # =============== 结束-学习唱歌任务 =================

            status = status_json["status"]  # status: "processing" "processed" "waiting" "error"
            songname = status_json["songName"]
            self.log.info(f"准备生成歌曲内容：{status_json}")

            # 如果状态是错误，直接返回失败
            if status == "error":
                self.log.error(f"歌曲《{songname}》转换任务创建失败")
                is_created = 2
            elif status == "processing" or status == "processed" or status == "waiting":
                i = 0
                vocal_downfile = None
                accompany_downfile = None
                song_path = f"./output/{songname}/"
                while (vocal_downfile is None or accompany_downfile is None) and self.singData.is_creating_song == 1:
                    # 检查歌曲是否生成成功
                    is_created = self.check_down_song(songname)
                    if is_created == 2:
                        break
                    i = i + 1
                    if i >= self.singData.create_song_timout:
                        print(f"歌曲《{songname}》生成失败" + "i >= self.singData.create_song_timout =" + str(i))
                        is_created = 2
                        break
                    self.obs.show_text("状态提示", f"当前学唱歌曲《{songname}》第{i}秒")#{self.llmData.Ai_Name}
                    self.log.info(f"生成《{songname}》歌曲第[{i}]秒,生成状态:{is_created}")
                    time.sleep(1)
            # =============== 结束生成歌曲 =================
        except Exception as e:
            print(e)
            self.log.exception(f"《{songname}》create_song异常：")
            is_created = 2
        finally:
            self.singData.is_creating_song = 2
            self.singData.create_song_lock.release()
        return is_created

    # 播放歌曲 1.成功 2.没有歌曲播放 3.异常
    def play_song(self,is_created, songname, song_path, username, query):
        try:
            # 播放歌曲
            if is_created == 1:
                self.log.info(f"准备唱歌《{songname}》,播放路径:{song_path}")
                # 开始唱歌服装穿戴
                self.emoteOper.emote_ws(1, 0.2, "唱歌")
                # 播报唱歌文字
                self.ttsCore.tts_say(f"回复{username}：我准备唱一首歌《{songname}》")
                # 循环摇摆动作
                auto_swing_thread = Thread(target=self.actionOper.auto_swing)
                auto_swing_thread.start()
                # ============== 播放音乐 ================
                speaker = music_convert_service.speaker if music_convert_service else "草神"

                # 检查并确定要播放的文件
                vocal_file = None
                if os.path.exists(song_path + f"{songname}_{speaker}.wav"):
                    # 使用完整的转换后文件
                    vocal_file = song_path + f"{songname}_{speaker}.wav"
                elif os.path.exists(song_path + f"Vocals_{speaker}.wav"):
                    # 使用人声转换文件
                    vocal_file = song_path + f"Vocals_{speaker}.wav"
                elif os.path.exists(song_path + f"vocal_{speaker}.wav"):
                    # 兼容旧格式
                    vocal_file = song_path + f"vocal_{speaker}.wav"

                if vocal_file and os.path.exists(vocal_file):
                    # 伴奏播放
                    accompany_thread = Thread(
                        target=self.sing_play,
                        args=("accompany.exe", song_path + "Chord.wav", 100, "0")
                    )
                    # 调用音乐播放器[人声播放]
                    mpv_play_thread = Thread(
                        target=self.sing_play,
                        args=("song.exe", vocal_file, 100, "0"),
                    )
                    accompany_thread.start()
                    mpv_play_thread.start()
                else:
                    self.log.error(f"找不到可播放的人声文件: {song_path}")
                    return 2
                # ================ end ==================
                # 循环等待唱歌结束标志
                time.sleep(3)
                while self.singData.sing_play_flag == 1:
                    time.sleep(1)
                # 伴奏停止
                self.obs.control_video("伴奏", VideoControl.STOP.value)
                # 停止唱歌视频播放
                # self.obs.control_video("唱歌视频",VideoControl.STOP.value)
                # 结束唱歌穿戴
                self.emoteOper.emote_ws(1, 0.2, "唱歌")
                return 1
            else:
                tip = f"已经跳过歌曲《{songname}》，请稍后再点播"
                self.log.info(tip)
                # 加入回复列表，并且后续合成语音
                self.ttsCore.tts_say(f"回复{username}：{tip}")
                return 2
        except Exception as e:
            print(e)
            self.log.exception(f"《{songname}》play_song异常：")
            return 3

    # 播放唱歌
    def sing_play(self, mpv_name, song_path, volume, start):
        self.singData.sing_play_flag = 1
        self.mpvPlay.mpv_play(mpv_name, song_path, volume, start)
        self.singData.sing_play_flag = 0

    # 唱歌线程
    def check_sing(self):
        if not self.singData.SongQueueList.empty():
            song_json = self.singData.SongQueueList.get()
            self.log.info(f"启动唱歌:{song_json}")
            # 启动唱歌
            sing_thread = Thread(
                target=self.singTry, args=(song_json["prompt"], song_json["username"])
            )
            sing_thread.start()

    # http接口：唱歌接口处理
    def http_sing(self,songname,username):
        self.log.info(f'http唱歌接口处理："{username}"点播歌曲《{songname}》')
        song_json = {"prompt": songname, "username": username}
        self.singData.SongQueueList.put(song_json)
        return

    # http接口：点播歌曲列表
    def http_songlist(self,CallBackForTest):
        jsonstr = []
        if len(self.singData.SongNowName) > 0:
            # 当前歌曲
            username = self.singData.SongNowName["username"]
            songname = self.singData.SongNowName["songname"]
            text = f"'{username}'点播《{songname}》"
            jsonstr.append({"songname": text})
        # 播放歌曲清单
        for i in range(self.singData.SongMenuList.qsize()):
            data = self.singData.SongMenuList.queue[i]
            username = data["username"]
            songname = data["songname"]
            text = f"'{username}'点播《{songname}》"
            jsonstr.append({"songname": text})
        str = '({"status": "成功","content": ' + json.dumps(jsonstr) + "})"
        return str

    def inner_sing(self, query, username):
        try:
            # 获取歌曲名称
            songname = query
            # 检查歌曲是否已经生成
            is_created = self.check_down_song(songname)
            if is_created != 1:
                self.log.info(f"歌曲《{songname}》未生成，重新生成")
                # 创建歌曲
                create_song_thread = Thread(
                    target=self.create_song, args=(songname, username)
                )
                create_song_thread.start()
                return 1
            else:
                self.log.info(f"歌曲《{songname}》已经生成，直接播放")
                # 播放歌曲
                self.play_song(songname, username, is_created)
                return 2
        except Exception as e:
            self.log.exception(f"唱歌内部异常：{e}")
            return 3

    # 唱歌入口处理
    def msg_deal(self, traceid, query, uid, user_name):
        # 唱歌
        text = ["唱一下", "唱一首", "唱歌", "点歌", "点播"]
        is_contain = StringUtil.has_string_reg_list(f"^{text}", query)
        if is_contain is not None:
            num = StringUtil.is_index_contain_string(text, query)
            queryExtract = query[num: len(query)]  # 提取提问语句
            queryExtract = queryExtract.strip()
            self.log.info(f"[{traceid}]唱歌提示：" + queryExtract)
            if queryExtract == "":
                return True
            song_json = {"traceid": traceid, "prompt": queryExtract, "username": user_name}
            self.singData.SongQueueList.put(song_json)
            
            # 积分
            print("sing----msg_deal-----oper_score-----", query)
            self.operScore.oper_score(uid, user_name, -2, "", "sing")

            return True
        return False

    # 判断字符是否存在歌曲此队列
    def exist_song_queues(self, queues, name):
        # 当前歌曲
        if "songname" in self.singData.SongNowName and self.singData.SongNowName["songname"] == name:
            return True
        # 歌单里歌曲
        for i in range(queues.qsize()):
            data = queues.queue[i]
            if data["songname"] == name:
                return True
        return False

    def check_down_song(self, songname):
        # 检查歌曲是否已经生成
        speaker = music_convert_service.speaker if music_convert_service else "草神"

        # 检查主要的转换后文件
        if os.path.exists(f"./output/{songname}/{songname}_{speaker}.wav"):
            return 1
        # 检查中间文件，表示正在处理中
        elif os.path.exists(f"./output/{songname}/Vocals_{speaker}.wav"):
            return 1
        elif os.path.exists(f"./output/{songname}/Vocals.wav"):
            return 0  # 还在处理中
        elif os.path.exists(f"./output/{songname}/Chord.wav"):
            return 1
        else:
            return 0